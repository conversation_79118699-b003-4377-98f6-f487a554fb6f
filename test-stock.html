<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>股票组件测试</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/hux-blog.css">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .sidebar-container {
            color: #bfbfbf;
            font-size: 14px;
        }
        .sidebar-container h5 {
            color: #808080;
            padding-bottom: 1em;
        }
        .sidebar-container h5 a {
            color: #808080 !important;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2 style="text-align: center; margin-bottom: 30px; color: #333;">股票组件测试</h2>
        
        <div class="sidebar-container">
            <!-- Stock Section -->
            <section>
                <hr><h5>STOCK MARKET</h5>
                <div class="stock-widget">
                    <div class="stock-item">
                        <div class="stock-name">贵州茅台</div>
                        <div class="stock-code">600519.SH</div>
                        <div class="stock-price" id="stock-price">加载中...</div>
                        <div class="stock-change" id="stock-change">--</div>
                        <div class="stock-update-time" id="stock-update-time">更新时间: --</div>
                    </div>
                </div>
            </section>
        </div>
        
        <div style="margin-top: 20px; text-align: center;">
            <button onclick="window.stockWidget.updateStockPrice()" class="btn btn-primary btn-sm">
                手动刷新
            </button>
        </div>
        
        <div style="margin-top: 20px; font-size: 12px; color: #666; text-align: center;">
            <p>说明：由于跨域限制，可能显示模拟数据。在实际部署环境中会尝试获取真实数据。</p>
        </div>
    </div>

    <script src="js/stock.js"></script>
    <script>
        // 页面加载完成后初始化股票组件
        document.addEventListener('DOMContentLoaded', function() {
            window.stockWidget = new StockWidget();
        });
    </script>
</body>
</html>
