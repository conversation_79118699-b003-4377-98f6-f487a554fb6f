<!-- Footer -->
<footer>
    <div class="container">
        <div class="row">
            <div class="col-lg-8 col-lg-offset-2 col-md-10 col-md-offset-1">

                <p class="copyright text-muted">
                    Copyright &copy; {{ site.title }} {{ site.time | date: '%Y' }}
                </p>
            </div>
        </div>
    </div>
</footer>

<!-- jQuery -->
<script src="//cdn.bootcss.com/jquery/2.2.2/jquery.min.js"></script>

<!-- jQuery -->
<script src="{{ "/js/jquery.min.js " | prepend: site.baseurl }}"></script>

<div class="cb-search-tool">
    <input type="text" class="form-control cb-search-content" id="cb-search-content" placeholder="搜索文章标题、内容、标签..." autocomplete="off">

    <div style="position: fixed; top: 16px; right: 16px;">
        <img src="/search/img/cb-close.png" id="cb-close-btn" alt="关闭搜索"/>
    </div>
</div>

<div style="position: fixed; right: 16px; bottom: 20px;">
    <img src="/search/img/cb-search.png" id="cb-search-btn" title="点击搜索 (或双击Ctrl)" alt="搜索"/>
</div>

<link rel="stylesheet" href="{{ "/search/css/cb-search.css" | prepend: site.baseurl }}">

<script src="{{ "/search/js/bootstrap3-typeahead.min.js" | prepend: site.baseurl }}"></script>
<script src="{{ "/search/js/cb-search.js" | prepend: site.baseurl }}"></script>

<!-- Bootstrap Core JavaScript -->
<script src="//cdn.bootcss.com/bootstrap/3.3.6/js/bootstrap.min.js"></script>

<!-- Bootstrap Core JavaScript -->
<script src="{{ "/js/bootstrap.min.js " | prepend: site.baseurl }}"></script>

<!-- Custom Theme JavaScript -->
<script src="{{ "/js/hux-blog.min.js " | prepend: site.baseurl }}"></script>

<!-- Stock Widget JavaScript -->
<script src="{{ "/js/stock.js" | prepend: site.baseurl }}"></script>

<!-- Service Worker -->
{% if site.service-worker %}
<script type="text/javascript">
    if(navigator.serviceWorker){
        // For security reasons, a service worker can only control the pages that are in the same directory level or below it. That's why we put sw.js at ROOT level.
        navigator.serviceWorker
            .register('/sw.js')
            .then((registration) => {console.log('Service Worker Registered. ', registration)})
            .catch((error) => {console.log('ServiceWorker registration failed: ', error)})
    }
</script>
{% endif %}


<!-- async load function -->
<script>
    function async(u, c) {
      var d = document, t = 'script',
          o = d.createElement(t),
          s = d.getElementsByTagName(t)[0];
      o.src = u;
      if (c) { o.addEventListener('load', function (e) { c(null, e); }, false); }
      s.parentNode.insertBefore(o, s);
    }
</script>

<!--
     Because of the native support for backtick-style fenced code blocks
     right within the Markdown is landed in Github Pages,
     From V1.6, There is no need for Highlight.js,
     so Huxblog drops it officially.

     - https://github.com/blog/2100-github-pages-now-faster-and-simpler-with-jekyll-3-0
     - https://help.github.com/articles/creating-and-highlighting-code-blocks/
     - https://github.com/jneen/rouge/wiki/list-of-supported-languages-and-lexers
-->
<!--
    <script>
        async("http://cdn.bootcss.com/highlight.js/8.6/highlight.min.js", function(){
            hljs.initHighlightingOnLoad();
        })
    </script>
    <link href="http://cdn.bootcss.com/highlight.js/8.6/styles/github.min.css" rel="stylesheet">
-->


<!-- jquery.tagcloud.js -->
<script>
    // only load tagcloud.js in tag.html
    if($('#tag_cloud').length !== 0){
        async('{{ "/js/jquery.tagcloud.js" | prepend: site.baseurl }}',function(){
            $.fn.tagcloud.defaults = {
                //size: {start: 1, end: 1, unit: 'em'},
                color: {start: '#bbbbee', end: '#0085a1'},
            };
            $('#tag_cloud a').tagcloud();
        })
    }
</script>

<!--fastClick.js -->
<script>
    async("//cdnjs.cloudflare.com/ajax/libs/fastclick/1.0.6/fastclick.min.js", function(){
        var $nav = document.querySelector("nav");
        if($nav) FastClick.attach($nav);
    })
</script>


<!-- Google Analytics -->
{% if site.ga_track_id %}
<script>
    // dynamic User by Hux
    var _gaId = '{{ site.ga_track_id }}';
    var _gaDomain = '{{ site.ga_domain }}';

    // Originial
    (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
    (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
    m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
    })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

    ga('create', _gaId, _gaDomain);
    ga('send', 'pageview');
</script>
{% endif %}


<!-- Baidu Tongji -->
{% if site.ba_track_id %}
<script>
    // dynamic User by Hux
    var _baId = '{{ site.ba_track_id }}';

    // Originial
    var _hmt = _hmt || [];
    (function() {
      var hm = document.createElement("script");
      hm.src = "//hm.baidu.com/hm.js?" + _baId;
      var s = document.getElementsByTagName("script")[0];
      s.parentNode.insertBefore(hm, s);
    })();
</script>
{% endif %}



<!-- Side Catalog -->
{% if page.catalog %}
<script type="text/javascript">
    function generateCatalog (selector) {
        var P = $('div.post-container'),a,n,t,l,i,c;
        a = P.find('h1,h2,h3,h4,h5,h6');
        a.each(function () {
            n = $(this).prop('tagName').toLowerCase();
            i = "#"+$(this).prop('id');
            t = $(this).text();
            c = $('<a href="'+i+'" rel="nofollow">'+t+'</a>');
            l = $('<li class="'+n+'_nav"></li>').append(c);
            $(selector).append(l);
        });
        return true;
    }

    generateCatalog(".catalog-body");

    // toggle side catalog
    $(".catalog-toggle").click((function(e){
        e.preventDefault();
        $('.side-catalog').toggleClass("fold")
    }))

    /*
     * Doc: https://github.com/davist11/jQuery-One-Page-Nav
     * Fork by Hux to support padding
     */
    async("{{ '/js/jquery.nav.js' | prepend: site.baseurl }}", function () {
        $('.catalog-body').onePageNav({
            currentClass: "active",
            changeHash: !1,
            easing: "swing",
            filter: "",
            scrollSpeed: 700,
            scrollOffset: 0,
            scrollThreshold: .2,
            begin: null,
            end: null,
            scrollChange: null,
            padding: 80
        });
    });
</script>
{% endif %}

