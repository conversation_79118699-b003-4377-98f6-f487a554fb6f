/**
 * 股票价格获取和显示功能
 * 获取贵州茅台(600519.SH)的实时股票数据
 */

class StockWidget {
    constructor() {
        this.stockCode = '600519'; // 贵州茅台股票代码
        this.updateInterval = 30000; // 30秒更新一次
        this.isMarketOpen = this.checkMarketTime();
        this.init();
    }

    init() {
        this.updateStockPrice();
        // 只在交易时间内自动更新
        if (this.isMarketOpen) {
            setInterval(() => {
                this.updateStockPrice();
            }, this.updateInterval);
        }
    }

    // 检查是否在交易时间内
    checkMarketTime() {
        const now = new Date();
        const day = now.getDay(); // 0=周日, 1=周一, ..., 6=周六
        const hour = now.getHours();
        const minute = now.getMinutes();
        const currentTime = hour * 100 + minute;

        // 周末不交易
        if (day === 0 || day === 6) {
            return false;
        }

        // 交易时间: 9:30-11:30, 13:00-15:00
        const morningStart = 930;
        const morningEnd = 1130;
        const afternoonStart = 1300;
        const afternoonEnd = 1500;

        return (currentTime >= morningStart && currentTime <= morningEnd) ||
               (currentTime >= afternoonStart && currentTime <= afternoonEnd);
    }

    // 获取股票数据
    async updateStockPrice() {
        try {
            // 首先尝试获取真实数据
            this.fetchRealStockData();
        } catch (error) {
            console.error('获取股票数据失败:', error);
            // 如果获取失败，使用模拟数据
            this.generateMockData();
        }
    }

    // 尝试获取真实股票数据
    fetchRealStockData() {
        const script = document.createElement('script');
        const timestamp = Date.now();
        let dataReceived = false;

        // 创建全局回调函数
        window.stockDataCallback = (data) => {
            dataReceived = true;
            this.parseRealStockData(data);
            if (document.head.contains(script)) {
                document.head.removeChild(script);
            }
        };

        // 使用新浪财经API
        script.src = `https://hq.sinajs.cn/list=sh${this.stockCode}`;
        script.onerror = () => {
            if (!dataReceived) {
                this.generateMockData();
            }
            if (document.head.contains(script)) {
                document.head.removeChild(script);
            }
        };

        // 设置超时，如果5秒内没有数据则使用模拟数据
        setTimeout(() => {
            if (!dataReceived) {
                this.generateMockData();
                if (document.head.contains(script)) {
                    document.head.removeChild(script);
                }
            }
        }, 5000);

        document.head.appendChild(script);
    }

    // 生成模拟股票数据
    generateMockData() {
        // 基础价格范围 (贵州茅台大概价格区间)
        const basePrice = 1650;
        const variation = 100;

        // 生成随机价格变化
        const randomChange = (Math.random() - 0.5) * variation;
        const currentPrice = basePrice + randomChange;
        const previousClose = basePrice;
        const change = currentPrice - previousClose;
        const changePercent = ((change / previousClose) * 100).toFixed(2);

        this.displayStockData({
            name: '贵州茅台',
            price: currentPrice.toFixed(2),
            change: change.toFixed(2),
            changePercent: changePercent,
            updateTime: new Date().toLocaleTimeString('zh-CN'),
            isMock: true
        });
    }

    // 解析真实股票数据
    parseRealStockData(rawData) {
        try {
            // 新浪API返回格式: var hq_str_sh600519="贵州茅台,1756.000,1780.000,1756.000,1780.000,1750.000,1756.000,1757.000,..."
            let dataString = rawData;

            if (!dataString || typeof dataString !== 'string') {
                // 如果没有返回数据，尝试从全局变量获取
                if (window.hq_str_sh600519) {
                    dataString = window.hq_str_sh600519;
                } else {
                    this.generateMockData();
                    return;
                }
            }

            // 提取引号内的数据
            const match = dataString.match(/"([^"]+)"/);
            if (!match) {
                this.generateMockData();
                return;
            }

            const stockInfo = match[1].split(',');

            if (stockInfo.length < 10) {
                this.generateMockData();
                return;
            }

            const stockName = stockInfo[0];
            const currentPrice = parseFloat(stockInfo[3]);
            const previousClose = parseFloat(stockInfo[2]);
            const change = currentPrice - previousClose;
            const changePercent = previousClose > 0 ? ((change / previousClose) * 100).toFixed(2) : '0.00';

            this.displayStockData({
                name: stockName,
                price: currentPrice.toFixed(2),
                change: change.toFixed(2),
                changePercent: changePercent,
                updateTime: new Date().toLocaleTimeString('zh-CN'),
                isMock: false
            });

        } catch (error) {
            console.error('解析股票数据失败:', error);
            this.generateMockData();
        }
    }

    // 显示股票数据
    displayStockData(data) {
        const priceElement = document.getElementById('stock-price');
        const changeElement = document.getElementById('stock-change');
        const timeElement = document.getElementById('stock-update-time');

        if (priceElement) {
            priceElement.textContent = `¥${data.price}`;
        }

        if (changeElement) {
            const changeText = `${data.change >= 0 ? '+' : ''}${data.change} (${data.changePercent}%)`;
            changeElement.textContent = changeText;

            // 根据涨跌设置颜色
            changeElement.className = 'stock-change';
            if (data.change > 0) {
                changeElement.classList.add('positive');
            } else if (data.change < 0) {
                changeElement.classList.add('negative');
            } else {
                changeElement.classList.add('neutral');
            }
        }

        if (timeElement) {
            const updateText = `更新时间: ${data.updateTime}`;
            const mockText = data.isMock ? ' (模拟数据)' : '';
            timeElement.textContent = updateText + mockText;
        }
    }

    // 显示错误信息
    showError() {
        const priceElement = document.getElementById('stock-price');
        const changeElement = document.getElementById('stock-change');
        const timeElement = document.getElementById('stock-update-time');

        if (priceElement) {
            priceElement.textContent = '数据获取失败';
        }
        if (changeElement) {
            changeElement.textContent = '--';
            changeElement.className = 'stock-change neutral';
        }
        if (timeElement) {
            timeElement.textContent = '更新时间: --';
        }
    }
}

// 页面加载完成后初始化股票组件
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否存在股票组件
    if (document.querySelector('.stock-widget')) {
        new StockWidget();
    }
});
