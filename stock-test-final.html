<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>股票功能最终测试</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/hux-blog.css">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .test-container {
            max-width: 500px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .sidebar-container {
            color: #bfbfbf;
            font-size: 14px;
        }
        .sidebar-container h5 {
            color: #808080;
            padding-bottom: 1em;
        }
        .sidebar-container h5 a {
            color: #808080 !important;
            text-decoration: none;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            width: 100%;
        }
        .btn:hover {
            background: #0056b3;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2 style="text-align: center; color: #333; margin-bottom: 30px;">股票功能测试</h2>
        
        <div class="sidebar-container">
            <!-- Stock Section -->
            <section class="visible-md visible-lg">
                <hr><h5>STOCK MARKET</h5>
                <div class="stock-widget">
                    <div class="stock-item">
                        <div class="stock-name">贵州茅台</div>
                        <div class="stock-code">600519.SH</div>
                        <div class="stock-price" id="stock-price-moutai">加载中...</div>
                        <div class="stock-change" id="stock-change-moutai">--</div>
                        <div class="stock-update-time" id="stock-update-time-moutai">更新时间: --</div>
                    </div>
                    <div class="stock-item">
                        <div class="stock-name">腾讯控股</div>
                        <div class="stock-code">00700.HK</div>
                        <div class="stock-price" id="stock-price-tencent">加载中...</div>
                        <div class="stock-change" id="stock-change-tencent">--</div>
                        <div class="stock-update-time" id="stock-update-time-tencent">更新时间: --</div>
                    </div>
                </div>
            </section>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button class="btn" onclick="refreshStocks()">手动刷新股票数据</button>
        </div>
        
        <div class="status">
            <h4 style="margin-top: 0; color: #333;">状态信息</h4>
            <div id="status-info">等待加载...</div>
        </div>
        
        <div style="margin-top: 20px; font-size: 12px; color: #666; text-align: center;">
            <p><strong>说明：</strong></p>
            <p>1. 首先尝试网易财经API（支持JSONP）</p>
            <p>2. 如果失败，回退到新浪财经API</p>
            <p>3. 显示实时股票价格和涨跌幅</p>
            <p>4. 根据交易时间自动更新</p>
        </div>
    </div>

    <script src="js/stock.js"></script>
    <script>
        let stockWidget;
        let statusDiv = document.getElementById('status-info');
        
        function updateStatus(message) {
            const timestamp = new Date().toLocaleTimeString();
            statusDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            statusDiv.scrollTop = statusDiv.scrollHeight;
        }
        
        function refreshStocks() {
            updateStatus('手动刷新股票数据...');
            if (stockWidget) {
                stockWidget.updateAllStocks();
            } else {
                updateStatus('股票组件未初始化');
            }
        }
        
        // 重写console方法来显示状态
        const originalLog = console.log;
        const originalWarn = console.warn;
        const originalError = console.error;
        
        console.log = function(...args) {
            updateStatus('LOG: ' + args.join(' '));
            originalLog.apply(console, args);
        };
        
        console.warn = function(...args) {
            updateStatus('WARN: ' + args.join(' '));
            originalWarn.apply(console, args);
        };
        
        console.error = function(...args) {
            updateStatus('ERROR: ' + args.join(' '));
            originalError.apply(console, args);
        };
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('页面加载完成，初始化股票组件...');
            
            try {
                stockWidget = new StockWidget();
                updateStatus('股票组件创建成功');
                
                // 检查市场状态
                const aStockOpen = stockWidget.isAStockMarketOpen();
                const hkStockOpen = stockWidget.isHKStockMarketOpen();
                updateStatus(`A股市场: ${aStockOpen ? '交易中' : '休市'}`);
                updateStatus(`港股市场: ${hkStockOpen ? '交易中' : '休市'}`);
                
            } catch (error) {
                updateStatus('初始化失败: ' + error.message);
            }
        });
    </script>
</body>
</html>
