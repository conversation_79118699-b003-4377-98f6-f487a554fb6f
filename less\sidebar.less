@import "variables.less";

// Sidebar Components

// Large Screen
@media (min-width: 1200px){
    .post-container, .sidebar-container{
        padding-right: 5%;
    }
}
@media (min-width: 768px){
    .post-container{
        padding-right: 5%;
    }
}

// Container of Sidebar, also Friends
.sidebar-container{
    color: @gray-l;
    font-size: 14px;
    h5{
        color: @gray;
        padding-bottom: 1em;
        a{
            color: @gray !important;
            text-decoration: none;
        }
    }
    a{
        color: @gray-l !important;
        &:hover, &:active{
            color: @brand-primary !important;
        }
    }
    .tags{
        a{
            border-color: @gray-l;
            &:hover, &:active{
                border-color: @brand-primary;
            }
        }
    }
    .short-about{
        img{
            width: 80%;
            display: block;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        p{
            margin-top: 0px;
            margin-bottom: 20px;
        }
        .list-inline>li{
            padding-left: 0px;
        }
    }

    // Stock Widget Styles
    .stock-widget {
        .stock-item {
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;

            &:hover {
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
                transform: translateY(-1px);
            }

            .stock-name {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                margin-bottom: 5px;
            }

            .stock-code {
                font-size: 12px;
                color: @gray;
                margin-bottom: 10px;
            }

            .stock-price {
                font-size: 20px;
                font-weight: bold;
                color: #333;
                margin-bottom: 5px;
            }

            .stock-change {
                font-size: 14px;
                font-weight: 500;
                margin-bottom: 8px;

                &.positive {
                    color: #d32f2f;
                }

                &.negative {
                    color: #388e3c;
                }

                &.neutral {
                    color: @gray;
                }
            }

            .stock-update-time {
                font-size: 11px;
                color: @gray-l;
                text-align: right;
            }
        }
    }
}
