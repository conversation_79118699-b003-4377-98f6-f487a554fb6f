---
layout: default
---

<!-- Page Header -->
<header class="intro-header" style="background-image: url('{{ site.baseurl }}/{% if page.header-img %}{{ page.header-img }}{% else %}{{ site.header-img }}{% endif %}')">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 col-lg-offset-2 col-md-10 col-md-offset-1 ">
                <div class="site-heading">
                    <h1>{% if page.title %}{{ page.title }}{% else %}{{ site.title }}{% endif %}</h1>
                    <!--<hr class="small">-->
                    <span class="subheading">{{ page.description }}</span>
                </div>
            </div>
        </div>
    </div>
</header>

<!-- Main Content -->
<div class="container">
	<div class="row">
        {% if site.sidebar == false %}
<!-- NO SIDEBAR -->
    <!-- PostList Container -->
            <div class="col-lg-8 col-lg-offset-2 col-md-10 col-md-offset-1 postlist-container">
                {{ content }}
            </div>
    <!-- Sidebar Container -->
            <div class="
                col-lg-8 col-lg-offset-2
                col-md-10 col-md-offset-1
                sidebar-container">

                <!-- Featured Tags -->
                {% if site.featured-tags %}
                <section>
                    <!-- no hr -->
                    <h5><a href="{{'/tags/' | prepend: site.baseurl }}">FEATURED TAGS</a></h5>
                    <div class="tags">
        				{% for tag in site.tags %}
                            {% if tag[1].size > {{site.featured-condition-size}} %}
                				<a href="{{ site.baseurl }}/tags/#{{ tag[0] }}" title="{{ tag[0] }}" rel="{{ tag[1].size }}">
                                    {{ tag[0] }}
                                </a>
                            {% endif %}
        				{% endfor %}
        			</div>
                </section>
                {% endif %}


            </div>
        {% else %}

<!-- USE SIDEBAR -->
    <!-- PostList Container -->
    		<div class="
                col-lg-8 col-lg-offset-1
                col-md-8 col-md-offset-1
                col-sm-12
                col-xs-12
                postlist-container
            ">
    			{{ content }}
    		</div>
    <!-- Sidebar Container -->
            <div class="
                col-lg-3 col-lg-offset-0
                col-md-3 col-md-offset-0
                col-sm-12
                col-xs-12
                sidebar-container
            ">
                <!-- Featured Tags -->
                {% if site.featured-tags %}
                <section>
                    <hr class="hidden-sm hidden-xs">
                    <h5><a href="{{'/tags/' | prepend: site.baseurl }}">FEATURED TAGS</a></h5>
                    <div class="tags">
                        {% for tag in site.tags %}
                            {% if tag[1].size > {{site.featured-condition-size}} %}
                                <a href="{{ site.baseurl }}/tags/#{{ tag[0] }}" title="{{ tag[0] }}" rel="{{ tag[1].size }}">
                                    {{ tag[0] }}
                                </a>
                            {% endif %}
                        {% endfor %}
                    </div>
                </section>
                {% endif %}

                <!-- Short About -->
                <section class="visible-md visible-lg">
                    <hr><h5><a href="{{'/about/' | prepend: site.baseurl }}">ABOUT ME</a></h5>
                    <div class="short-about">
                        {% if site.sidebar-avatar %}
                            <a href="{{ site.baseurl }}/about">
                                <img src="{{site.sidebar-avatar}}"/>
                            </a>
                        {% endif %}
                        {% if site.sidebar-about-description %}
                            <p>{{site.sidebar-about-description}}</p>
                        {% endif %}

                        {% if site.email %}
                            <p>✉️ {{site.email}}</p>
                        {% endif %}
                    </div>
                </section>

                <!-- Stock Section -->
                <section class="visible-md visible-lg">
                    <hr><h5>STOCK MARKET</h5>
                    <div class="stock-widget">
                        <div class="stock-item">
                            <div class="stock-name">贵州茅台</div>
                            <div class="stock-code">600519.SH</div>
                            <div class="stock-price" id="stock-price">--</div>
                            <div class="stock-change" id="stock-change">--</div>
                            <div class="stock-update-time" id="stock-update-time">更新时间: --</div>
                        </div>
                    </div>
                </section>

    		</div>
        {% endif %}
	</div>
</div>
